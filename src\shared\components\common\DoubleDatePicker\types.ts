export interface DoubleDatePickerProps {
  /**
   * Gi<PERSON> trị của cặp DatePicker đầu tiên [startDate, endDate]
   */
  firstPairValue?: [Date | null, Date | null];
  
  /**
   * Giá trị của cặp DatePicker thứ hai [startDate, endDate]
   */
  secondPairValue?: [Date | null, Date | null];
  
  /**
   * Callback khi cặp DatePicker đầu tiên thay đổi
   */
  onFirstPairChange?: (dates: [Date | null, Date | null]) => void;
  
  /**
   * Callback khi cặp DatePicker thứ hai thay đổi
   */
  onSecondPairChange?: (dates: [Date | null, Date | null]) => void;
  
  /**
   * Placeholder cho cặp DatePicker đầu tiên
   */
  firstPairPlaceholder?: [string, string];
  
  /**
   * Placeholder cho cặp DatePicker thứ hai
   */
  secondPairPlaceholder?: [string, string];
  
  /**
   * Label cho cặp DatePicker đầu tiên
   */
  firstPairLabel?: string;
  
  /**
   * Label cho cặp DatePicker thứ hai
   */
  secondPairLabel?: string;
  
  /**
   * Icon hiển thị để mở dropdown
   */
  triggerIcon?: React.ReactNode;
  
  /**
   * <PERSON><PERSON><PERSON> thước của icon trigger
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Disabled component
   */
  disabled?: boolean;
  
  /**
   * Custom className
   */
  className?: string;
  
  /**
   * Ẩn border xung quanh icon
   */
  noBorder?: boolean;
}
