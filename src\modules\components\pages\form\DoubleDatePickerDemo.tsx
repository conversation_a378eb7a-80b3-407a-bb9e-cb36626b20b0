import React, { useState } from 'react';
import { 
  DoubleDatePicker, 
  Card, 
  Typography, 
  Icon,
  Button 
} from '@/shared/components/common';
import { ComponentDemo } from '../../components';

const DoubleDatePickerDemo: React.FC = () => {

  // State for different demo variants
  const [basicFirstPair, setBasicFirstPair] = useState<[Date | null, Date | null]>([null, null]);
  const [basicSecondPair, setBasicSecondPair] = useState<[Date | null, Date | null]>([null, null]);

  const [customFirstPair, setCustomFirstPair] = useState<[Date | null, Date | null]>([null, null]);
  const [customSecondPair, setCustomSecondPair] = useState<[Date | null, Date | null]>([null, null]);

  const [noBorderFirstPair, setNoBorderFirstPair] = useState<[Date | null, Date | null]>([null, null]);
  const [noBorderSecondPair, setNoBorderSecondPair] = useState<[Date | null, Date | null]>([null, null]);

  const [sizesFirstPair, setSizesFirstPair] = useState<[Date | null, Date | null]>([null, null]);
  const [sizesSecondPair, setSizesSecondPair] = useState<[Date | null, Date | null]>([null, null]);

  // Helper function to format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return 'Chưa chọn';
    return date.toLocaleDateString('vi-VN');
  };

  const formatDatePair = (pair: [Date | null, Date | null]) => {
    const [start, end] = pair;
    return `${formatDate(start)} - ${formatDate(end)}`;
  };

  const handleClearAll = () => {
    setBasicFirstPair([null, null]);
    setBasicSecondPair([null, null]);
    setCustomFirstPair([null, null]);
    setCustomSecondPair([null, null]);
    setNoBorderFirstPair([null, null]);
    setNoBorderSecondPair([null, null]);
    setSizesFirstPair([null, null]);
    setSizesSecondPair([null, null]);
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      <div className="flex items-center justify-between">
        <Typography variant="h1">DoubleDatePicker Demo</Typography>
        <Button onClick={handleClearAll} variant="outline" size="sm">
          <Icon name="refresh-cw" size="sm" className="mr-2" />
          Clear All
        </Button>
      </div>

      <Typography variant="body1" className="text-muted-foreground">
        DoubleDatePicker component hiển thị 2 cặp DatePicker khi bấm vào icon. 
        Thích hợp cho việc so sánh hoặc chọn nhiều khoảng thời gian.
      </Typography>

      {/* Basic DoubleDatePicker */}
      <ComponentDemo
        title="Basic DoubleDatePicker"
        description="DoubleDatePicker cơ bản với icon calendar mặc định."
        code={`import { DoubleDatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [firstPair, setFirstPair] = useState<[Date | null, Date | null]>([null, null]);
const [secondPair, setSecondPair] = useState<[Date | null, Date | null]>([null, null]);

// Render
<DoubleDatePicker
  firstPairValue={firstPair}
  secondPairValue={secondPair}
  onFirstPairChange={setFirstPair}
  onSecondPairChange={setSecondPair}
  firstPairLabel="Khoảng thời gian 1"
  secondPairLabel="Khoảng thời gian 2"
/>`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <DoubleDatePicker
            firstPairValue={basicFirstPair}
            secondPairValue={basicSecondPair}
            onFirstPairChange={setBasicFirstPair}
            onSecondPairChange={setBasicSecondPair}
            firstPairLabel="Khoảng thời gian 1"
            secondPairLabel="Khoảng thời gian 2"
          />
          
          {/* Display selected values */}
          <div className="mt-4 p-3 bg-muted rounded-lg text-sm space-y-2">
            <div>
              <span className="font-medium">Khoảng thời gian 1:</span> {formatDatePair(basicFirstPair)}
            </div>
            <div>
              <span className="font-medium">Khoảng thời gian 2:</span> {formatDatePair(basicSecondPair)}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* Custom Icon DoubleDatePicker */}
      <ComponentDemo
        title="Custom Icon DoubleDatePicker"
        description="DoubleDatePicker với icon tùy chỉnh và label tùy chỉnh."
        code={`import { DoubleDatePicker, Icon } from '@/shared/components/common';
import { useState } from 'react';

// State
const [firstPair, setFirstPair] = useState<[Date | null, Date | null]>([null, null]);
const [secondPair, setSecondPair] = useState<[Date | null, Date | null]>([null, null]);

// Render
<DoubleDatePicker
  firstPairValue={firstPair}
  secondPairValue={secondPair}
  onFirstPairChange={setFirstPair}
  onSecondPairChange={setSecondPair}
  firstPairLabel="Thời gian bắt đầu"
  secondPairLabel="Thời gian kết thúc"
  firstPairPlaceholder={['Từ', 'Đến']}
  secondPairPlaceholder={['Từ', 'Đến']}
  triggerIcon={<Icon name="clock" size="md" className="text-primary" />}
/>`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <DoubleDatePicker
            firstPairValue={customFirstPair}
            secondPairValue={customSecondPair}
            onFirstPairChange={setCustomFirstPair}
            onSecondPairChange={setCustomSecondPair}
            firstPairLabel="Thời gian bắt đầu"
            secondPairLabel="Thời gian kết thúc"
            firstPairPlaceholder={['Từ', 'Đến']}
            secondPairPlaceholder={['Từ', 'Đến']}
            triggerIcon={<Icon name="clock" size="md" className="text-primary" />}
          />
          
          {/* Display selected values */}
          <div className="mt-4 p-3 bg-muted rounded-lg text-sm space-y-2">
            <div>
              <span className="font-medium">Thời gian bắt đầu:</span> {formatDatePair(customFirstPair)}
            </div>
            <div>
              <span className="font-medium">Thời gian kết thúc:</span> {formatDatePair(customSecondPair)}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* No Border DoubleDatePicker */}
      <ComponentDemo
        title="No Border DoubleDatePicker"
        description="DoubleDatePicker không có border xung quanh icon."
        code={`import { DoubleDatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [firstPair, setFirstPair] = useState<[Date | null, Date | null]>([null, null]);
const [secondPair, setSecondPair] = useState<[Date | null, Date | null]>([null, null]);

// Render
<DoubleDatePicker
  firstPairValue={firstPair}
  secondPairValue={secondPair}
  onFirstPairChange={setFirstPair}
  onSecondPairChange={setSecondPair}
  noBorder
/>`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <DoubleDatePicker
            firstPairValue={noBorderFirstPair}
            secondPairValue={noBorderSecondPair}
            onFirstPairChange={setNoBorderFirstPair}
            onSecondPairChange={setNoBorderSecondPair}
            noBorder
          />
          
          {/* Display selected values */}
          <div className="mt-4 p-3 bg-muted rounded-lg text-sm space-y-2">
            <div>
              <span className="font-medium">Khoảng thời gian 1:</span> {formatDatePair(noBorderFirstPair)}
            </div>
            <div>
              <span className="font-medium">Khoảng thời gian 2:</span> {formatDatePair(noBorderSecondPair)}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* Different Sizes */}
      <ComponentDemo
        title="Different Sizes"
        description="DoubleDatePicker với các kích thước khác nhau."
        code={`import { DoubleDatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [firstPair, setFirstPair] = useState<[Date | null, Date | null]>([null, null]);
const [secondPair, setSecondPair] = useState<[Date | null, Date | null]>([null, null]);

// Render
<div className="flex items-center space-x-4">
  <DoubleDatePicker size="sm" />
  <DoubleDatePicker size="md" />
  <DoubleDatePicker size="lg" />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <div className="flex items-center justify-center space-x-4">
            <DoubleDatePicker
              firstPairValue={sizesFirstPair}
              secondPairValue={sizesSecondPair}
              onFirstPairChange={setSizesFirstPair}
              onSecondPairChange={setSizesSecondPair}
              size="sm"
            />
            <DoubleDatePicker
              firstPairValue={sizesFirstPair}
              secondPairValue={sizesSecondPair}
              onFirstPairChange={setSizesFirstPair}
              onSecondPairChange={setSizesSecondPair}
              size="md"
            />
            <DoubleDatePicker
              firstPairValue={sizesFirstPair}
              secondPairValue={sizesSecondPair}
              onFirstPairChange={setSizesFirstPair}
              onSecondPairChange={setSizesSecondPair}
              size="lg"
            />
          </div>
          
          {/* Display selected values */}
          <div className="mt-4 p-3 bg-muted rounded-lg text-sm space-y-2">
            <div>
              <span className="font-medium">Khoảng thời gian 1:</span> {formatDatePair(sizesFirstPair)}
            </div>
            <div>
              <span className="font-medium">Khoảng thời gian 2:</span> {formatDatePair(sizesSecondPair)}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* Usage Guide */}
      <Card title="DoubleDatePicker Usage Guide" className="mb-6">
        <div className="space-y-4">
          <Typography variant="body2" className="font-medium">Basic Usage:</Typography>
          <pre className="bg-muted p-4 rounded text-sm overflow-auto">
            {`import { DoubleDatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State for date pairs
const [firstPair, setFirstPair] = useState<[Date | null, Date | null]>([null, null]);
const [secondPair, setSecondPair] = useState<[Date | null, Date | null]>([null, null]);

<DoubleDatePicker
  firstPairValue={firstPair}
  secondPairValue={secondPair}
  onFirstPairChange={setFirstPair}
  onSecondPairChange={setSecondPair}
  firstPairLabel="Khoảng thời gian 1"
  secondPairLabel="Khoảng thời gian 2"
/>`}
          </pre>

          <Typography variant="body2" className="font-medium mt-4">Key Features:</Typography>
          <ul className="list-disc list-inside space-y-1 pl-4 text-sm">
            <li>Sử dụng <code>noBorder</code> để ẩn border xung quanh icon</li>
            <li>Sử dụng <code>triggerIcon</code> để thay đổi icon hiển thị</li>
            <li>Sử dụng <code>size</code> để thay đổi kích thước icon (sm, md, lg)</li>
            <li>Sử dụng <code>firstPairLabel</code> và <code>secondPairLabel</code> để đặt tên cho từng cặp</li>
            <li>Sử dụng <code>firstPairPlaceholder</code> và <code>secondPairPlaceholder</code> để tùy chỉnh placeholder</li>
            <li>Sử dụng <code>disabled</code> để vô hiệu hóa component</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default DoubleDatePickerDemo;
