import React, { useState } from 'react';
import { 
  DoubleDatePicker, 
  Card, 
  Typography, 
  Icon,
  Button 
} from '@/shared/components/common';
import { ComponentDemo } from '../../components';

const DoubleDatePickerDemo: React.FC = () => {

  // State for different demo variants
  const [basicRange, setBasicRange] = useState<[Date | null, Date | null]>([null, null]);
  const [customRange, setCustomRange] = useState<[Date | null, Date | null]>([null, null]);
  const [noBorderRange, setNoBorderRange] = useState<[Date | null, Date | null]>([null, null]);
  const [sizesRange, setSizesRange] = useState<[Date | null, Date | null]>([null, null]);

  // Helper function to format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return 'Chưa chọn';
    return date.toLocaleDateString('vi-VN');
  };

  const formatDatePair = (pair: [Date | null, Date | null]) => {
    const [start, end] = pair;
    return `${formatDate(start)} - ${formatDate(end)}`;
  };

  const handleClearAll = () => {
    setBasicRange([null, null]);
    setCustomRange([null, null]);
    setNoBorderRange([null, null]);
    setSizesRange([null, null]);
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      <div className="flex items-center justify-between">
        <Typography variant="h1">DoubleDatePicker Demo</Typography>
        <Button onClick={handleClearAll} variant="outline" size="sm">
          <Icon name="refresh-cw" size="sm" className="mr-2" />
          Clear All
        </Button>
      </div>

      <Typography variant="body1" className="text-muted-foreground">
        DoubleDatePicker component hiển thị 2 tháng calendar liền nhau khi bấm vào icon.
        Thích hợp cho việc chọn khoảng thời gian (range picker).
      </Typography>

      {/* Basic DoubleDatePicker */}
      <ComponentDemo
        title="Basic DoubleDatePicker"
        description="DoubleDatePicker cơ bản với icon calendar mặc định, hiển thị 2 tháng liền nhau."
        code={`import { DoubleDatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

// Render
<DoubleDatePicker
  value={dateRange}
  onChange={setDateRange}
  placeholder="Chọn khoảng thời gian"
/>`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <DoubleDatePicker
            value={basicRange}
            onChange={setBasicRange}
            placeholder="Chọn khoảng thời gian"
          />

          {/* Display selected values */}
          <div className="mt-4 p-3 bg-muted rounded-lg text-sm">
            <div>
              <span className="font-medium">Khoảng thời gian đã chọn:</span> {formatDatePair(basicRange)}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* Custom Icon DoubleDatePicker */}
      <ComponentDemo
        title="Custom Icon DoubleDatePicker"
        description="DoubleDatePicker với icon tùy chỉnh."
        code={`import { DoubleDatePicker, Icon } from '@/shared/components/common';
import { useState } from 'react';

// State
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

// Render
<DoubleDatePicker
  value={dateRange}
  onChange={setDateRange}
  triggerIcon={<Icon name="clock" size="md" className="text-primary" />}
  placeholder="Chọn thời gian"
/>`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <DoubleDatePicker
            value={customRange}
            onChange={setCustomRange}
            triggerIcon={<Icon name="clock" size="md" className="text-primary" />}
            placeholder="Chọn thời gian"
          />

          {/* Display selected values */}
          <div className="mt-4 p-3 bg-muted rounded-lg text-sm">
            <div>
              <span className="font-medium">Thời gian đã chọn:</span> {formatDatePair(customRange)}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* No Border DoubleDatePicker */}
      <ComponentDemo
        title="No Border DoubleDatePicker"
        description="DoubleDatePicker không có border xung quanh icon."
        code={`import { DoubleDatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

// Render
<DoubleDatePicker
  value={dateRange}
  onChange={setDateRange}
  noBorder
/>`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <DoubleDatePicker
            value={noBorderRange}
            onChange={setNoBorderRange}
            noBorder
          />

          {/* Display selected values */}
          <div className="mt-4 p-3 bg-muted rounded-lg text-sm">
            <div>
              <span className="font-medium">Khoảng thời gian đã chọn:</span> {formatDatePair(noBorderRange)}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* Different Sizes */}
      <ComponentDemo
        title="Different Sizes"
        description="DoubleDatePicker với các kích thước khác nhau."
        code={`import { DoubleDatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

// Render
<div className="flex items-center space-x-4">
  <DoubleDatePicker value={dateRange} onChange={setDateRange} size="sm" />
  <DoubleDatePicker value={dateRange} onChange={setDateRange} size="md" />
  <DoubleDatePicker value={dateRange} onChange={setDateRange} size="lg" />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <div className="flex items-center justify-center space-x-4">
            <DoubleDatePicker
              value={sizesRange}
              onChange={setSizesRange}
              size="sm"
            />
            <DoubleDatePicker
              value={sizesRange}
              onChange={setSizesRange}
              size="md"
            />
            <DoubleDatePicker
              value={sizesRange}
              onChange={setSizesRange}
              size="lg"
            />
          </div>

          {/* Display selected values */}
          <div className="mt-4 p-3 bg-muted rounded-lg text-sm">
            <div>
              <span className="font-medium">Khoảng thời gian đã chọn:</span> {formatDatePair(sizesRange)}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* Usage Guide */}
      <Card title="DoubleDatePicker Usage Guide" className="mb-6">
        <div className="space-y-4">
          <Typography variant="body2" className="font-medium">Basic Usage:</Typography>
          <pre className="bg-muted p-4 rounded text-sm overflow-auto">
            {`import { DoubleDatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State for date pairs
const [firstPair, setFirstPair] = useState<[Date | null, Date | null]>([null, null]);
const [secondPair, setSecondPair] = useState<[Date | null, Date | null]>([null, null]);

<DoubleDatePicker
  firstPairValue={firstPair}
  secondPairValue={secondPair}
  onFirstPairChange={setFirstPair}
  onSecondPairChange={setSecondPair}
  firstPairLabel="Khoảng thời gian 1"
  secondPairLabel="Khoảng thời gian 2"
/>`}
          </pre>

          <Typography variant="body2" className="font-medium mt-4">Key Features:</Typography>
          <ul className="list-disc list-inside space-y-1 pl-4 text-sm">
            <li>Sử dụng <code>noBorder</code> để ẩn border xung quanh icon</li>
            <li>Sử dụng <code>triggerIcon</code> để thay đổi icon hiển thị</li>
            <li>Sử dụng <code>size</code> để thay đổi kích thước icon (sm, md, lg)</li>
            <li>Sử dụng <code>firstPairLabel</code> và <code>secondPairLabel</code> để đặt tên cho từng cặp</li>
            <li>Sử dụng <code>firstPairPlaceholder</code> và <code>secondPairPlaceholder</code> để tùy chỉnh placeholder</li>
            <li>Sử dụng <code>disabled</code> để vô hiệu hóa component</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default DoubleDatePickerDemo;
