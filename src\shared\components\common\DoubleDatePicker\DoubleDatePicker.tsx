import React, { useState, forwardRef } from 'react';
import { DatePicker, Icon } from '@/shared/components/common';
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useClick,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  FloatingFocusManager,
} from '@floating-ui/react';

export interface DoubleDatePickerProps {
  /**
   * Giá trị của cặp DatePicker đầu tiên [startDate, endDate]
   */
  firstPairValue?: [Date | null, Date | null];
  
  /**
   * Giá trị của cặp DatePicker thứ hai [startDate, endDate]
   */
  secondPairValue?: [Date | null, Date | null];
  
  /**
   * Callback khi cặp DatePicker đầu tiên thay đổi
   */
  onFirstPairChange?: (dates: [Date | null, Date | null]) => void;
  
  /**
   * Callback khi cặp DatePicker thứ hai thay đổi
   */
  onSecondPairChange?: (dates: [Date | null, Date | null]) => void;
  
  /**
   * Placeholder cho cặp DatePicker đầu tiên
   */
  firstPairPlaceholder?: [string, string];
  
  /**
   * Placeholder cho cặp DatePicker thứ hai
   */
  secondPairPlaceholder?: [string, string];
  
  /**
   * Label cho cặp DatePicker đầu tiên
   */
  firstPairLabel?: string;
  
  /**
   * Label cho cặp DatePicker thứ hai
   */
  secondPairLabel?: string;
  
  /**
   * Icon hiển thị để mở dropdown
   */
  triggerIcon?: React.ReactNode;
  
  /**
   * Kích thước của icon trigger
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Disabled component
   */
  disabled?: boolean;
  
  /**
   * Custom className
   */
  className?: string;
  
  /**
   * Ẩn border xung quanh icon
   */
  noBorder?: boolean;
}

/**
 * DoubleDatePicker component hiển thị 2 cặp DatePicker khi bấm vào icon
 * 
 * @example
 * ```tsx
 * import { DoubleDatePicker } from '@/shared/components/common';
 * import { useState } from 'react';
 * 
 * const MyComponent = () => {
 *   const [firstPair, setFirstPair] = useState<[Date | null, Date | null]>([null, null]);
 *   const [secondPair, setSecondPair] = useState<[Date | null, Date | null]>([null, null]);
 * 
 *   return (
 *     <DoubleDatePicker
 *       firstPairValue={firstPair}
 *       secondPairValue={secondPair}
 *       onFirstPairChange={setFirstPair}
 *       onSecondPairChange={setSecondPair}
 *       firstPairLabel="Khoảng thời gian 1"
 *       secondPairLabel="Khoảng thời gian 2"
 *     />
 *   );
 * };
 * ```
 */
const DoubleDatePicker = forwardRef<HTMLDivElement, DoubleDatePickerProps>(
  (
    {
      firstPairValue = [null, null],
      secondPairValue = [null, null],
      onFirstPairChange,
      onSecondPairChange,
      firstPairPlaceholder = ['Từ ngày', 'Đến ngày'],
      secondPairPlaceholder = ['Từ ngày', 'Đến ngày'],
      firstPairLabel = 'Khoảng thời gian 1',
      secondPairLabel = 'Khoảng thời gian 2',
      triggerIcon,
      size = 'md',
      disabled = false,
      className = '',
      noBorder = false,
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      middleware: [
        offset(8),
        flip({
          fallbackAxisSideDirection: 'start',
        }),
        shift({ padding: 8 }),
      ],
      whileElementsMounted: autoUpdate,
      placement: 'bottom-start',
    });

    const click = useClick(context);
    const dismiss = useDismiss(context);
    const role = useRole(context);

    const { getReferenceProps, getFloatingProps } = useInteractions([
      click,
      dismiss,
      role,
    ]);

    const handleFirstPairStartChange = (date: Date | null) => {
      const newPair: [Date | null, Date | null] = [date, firstPairValue[1]];
      onFirstPairChange?.(newPair);
    };

    const handleFirstPairEndChange = (date: Date | null) => {
      const newPair: [Date | null, Date | null] = [firstPairValue[0], date];
      onFirstPairChange?.(newPair);
    };

    const handleSecondPairStartChange = (date: Date | null) => {
      const newPair: [Date | null, Date | null] = [date, secondPairValue[1]];
      onSecondPairChange?.(newPair);
    };

    const handleSecondPairEndChange = (date: Date | null) => {
      const newPair: [Date | null, Date | null] = [secondPairValue[0], date];
      onSecondPairChange?.(newPair);
    };

    // Icon size mapping
    const iconSizeMap = {
      sm: 'sm' as const,
      md: 'md' as const,
      lg: 'lg' as const,
    };

    // Button size classes
    const buttonSizeClasses = {
      sm: 'h-8 w-8',
      md: 'h-10 w-10',
      lg: 'h-12 w-12',
    };

    const defaultIcon = (
      <Icon 
        name="calendar" 
        size={iconSizeMap[size]} 
        className="text-muted-foreground hover:text-foreground transition-colors" 
      />
    );

    const buttonClasses = `
      inline-flex items-center justify-center rounded-md
      ${buttonSizeClasses[size]}
      ${noBorder 
        ? 'hover:bg-accent hover:text-accent-foreground' 
        : 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
      }
      ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
      transition-colors
      ${className}
    `.trim();

    return (
      <div ref={ref} className="relative">
        {/* Trigger Button */}
        <button
          ref={refs.setReference}
          className={buttonClasses}
          disabled={disabled}
          {...getReferenceProps()}
        >
          {triggerIcon || defaultIcon}
        </button>

        {/* Dropdown Content */}
        {isOpen && (
          <FloatingPortal>
            <FloatingFocusManager context={context} modal={false}>
              <div
                ref={refs.setFloating}
                style={floatingStyles}
                className="z-[9200] bg-popover border border-border rounded-lg shadow-lg p-4 min-w-[400px]"
                {...getFloatingProps()}
              >
                <div className="space-y-6">
                  {/* First Pair */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-foreground">{firstPairLabel}</h4>
                    <div className="flex gap-3">
                      <DatePicker
                        value={firstPairValue[0]}
                        onChange={handleFirstPairStartChange}
                        placeholder={firstPairPlaceholder[0]}
                        size="sm"
                        fullWidth
                      />
                      <DatePicker
                        value={firstPairValue[1]}
                        onChange={handleFirstPairEndChange}
                        placeholder={firstPairPlaceholder[1]}
                        size="sm"
                        fullWidth
                      />
                    </div>
                  </div>

                  {/* Divider */}
                  <div className="border-t border-border" />

                  {/* Second Pair */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-foreground">{secondPairLabel}</h4>
                    <div className="flex gap-3">
                      <DatePicker
                        value={secondPairValue[0]}
                        onChange={handleSecondPairStartChange}
                        placeholder={secondPairPlaceholder[0]}
                        size="sm"
                        fullWidth
                      />
                      <DatePicker
                        value={secondPairValue[1]}
                        onChange={handleSecondPairEndChange}
                        placeholder={secondPairPlaceholder[1]}
                        size="sm"
                        fullWidth
                      />
                    </div>
                  </div>
                </div>
              </div>
            </FloatingFocusManager>
          </FloatingPortal>
        )}
      </div>
    );
  }
);

DoubleDatePicker.displayName = 'DoubleDatePicker';

export default DoubleDatePicker;
