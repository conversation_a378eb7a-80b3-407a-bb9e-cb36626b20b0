import React, { useState, forwardRef } from 'react';
import { Icon } from '@/shared/components/common';
import { addMonths, format, startOfMonth, endOfMonth, isSameMonth, isToday, isSameDay } from 'date-fns';
import { vi } from 'date-fns/locale';
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useClick,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  FloatingFocusManager,
} from '@floating-ui/react';

export interface DoubleDatePickerProps {
  /**
   * Giá trị đã chọn [startDate, endDate]
   */
  value?: [Date | null, Date | null];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (dates: [Date | null, Date | null]) => void;

  /**
   * Icon hiển thị để mở dropdown
   */
  triggerIcon?: React.ReactNode;

  /**
   * <PERSON>ích thước của icon trigger
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Disabled component
   */
  disabled?: boolean;

  /**
   * Custom className
   */
  className?: string;

  /**
   * Ẩn border xung quanh icon
   */
  noBorder?: boolean;

  /**
   * Placeholder text
   */
  placeholder?: string;
}

// Helper function to generate calendar days
const generateCalendarDays = (month: Date) => {
  const start = startOfMonth(month);
  const end = endOfMonth(month);
  const days = [];

  // Get first day of week (0 = Sunday, 1 = Monday, etc.)
  const firstDayOfWeek = start.getDay();

  // Add empty cells for days before month starts
  for (let i = 0; i < firstDayOfWeek; i++) {
    days.push(null);
  }

  // Add all days of the month
  for (let day = 1; day <= end.getDate(); day++) {
    days.push(new Date(month.getFullYear(), month.getMonth(), day));
  }

  return days;
};

/**
 * DoubleDatePicker component hiển thị 2 tháng calendar liền nhau khi bấm vào icon
 *
 * @example
 * ```tsx
 * import { DoubleDatePicker } from '@/shared/components/common';
 * import { useState } from 'react';
 *
 * const MyComponent = () => {
 *   const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
 *
 *   return (
 *     <DoubleDatePicker
 *       value={dateRange}
 *       onChange={setDateRange}
 *       placeholder="Chọn khoảng thời gian"
 *     />
 *   );
 * };
 * ```
 */
const DoubleDatePicker = forwardRef<HTMLDivElement, DoubleDatePickerProps>(
  (
    {
      value = [null, null],
      onChange,
      triggerIcon,
      size = 'md',
      disabled = false,
      className = '',
      noBorder = false,
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [startDate, endDate] = value;

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      middleware: [
        offset(8),
        flip({
          fallbackAxisSideDirection: 'start',
        }),
        shift({ padding: 8 }),
      ],
      whileElementsMounted: autoUpdate,
      placement: 'bottom-start',
    });

    const click = useClick(context);
    const dismiss = useDismiss(context);
    const role = useRole(context);

    const { getReferenceProps, getFloatingProps } = useInteractions([
      click,
      dismiss,
      role,
    ]);

    // Handle date selection
    const handleDateClick = (date: Date) => {
      if (!startDate || (startDate && endDate)) {
        // Start new selection
        onChange?.([date, null]);
      } else if (startDate && !endDate) {
        // Complete selection
        if (date < startDate) {
          onChange?.([date, startDate]);
        } else {
          onChange?.([startDate, date]);
        }
      }
    };

    // Navigation functions
    const goToPreviousMonth = () => {
      setCurrentMonth(prev => addMonths(prev, -1));
    };

    const goToNextMonth = () => {
      setCurrentMonth(prev => addMonths(prev, 1));
    };

    // Get next month for second calendar
    const nextMonth = addMonths(currentMonth, 1);

    // Helper function to check if date is in range
    const isInRange = (date: Date) => {
      if (!startDate || !endDate) return false;
      return date >= startDate && date <= endDate;
    };

    // Helper function to check if date is start or end
    const isStartDate = (date: Date) => startDate && isSameDay(date, startDate);
    const isEndDate = (date: Date) => endDate && isSameDay(date, endDate);

    // Icon size mapping
    const iconSizeMap = {
      sm: 'sm' as const,
      md: 'md' as const,
      lg: 'lg' as const,
    };

    // Button size classes
    const buttonSizeClasses = {
      sm: 'h-8 w-8',
      md: 'h-10 w-10',
      lg: 'h-12 w-12',
    };

    const defaultIcon = (
      <Icon
        name="calendar"
        size={iconSizeMap[size]}
        className="text-muted-foreground hover:text-foreground transition-colors"
      />
    );

    const buttonClasses = `
      inline-flex items-center justify-center rounded-md
      ${buttonSizeClasses[size]}
      ${noBorder
        ? 'hover:bg-accent hover:text-accent-foreground'
        : 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
      }
      ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
      transition-colors
      ${className}
    `.trim();

    // Render single calendar month
    const renderCalendar = (month: Date, showNavigation: 'left' | 'right' | 'none' = 'none') => {
      const days = generateCalendarDays(month);
      const weekDays = ['CN', 'Th 2', 'Th 3', 'Th 4', 'Th 5', 'Th 6', 'Th 7'];

      return (
        <div className="p-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            {showNavigation === 'left' && (
              <button
                onClick={goToPreviousMonth}
                className="p-1 hover:bg-accent rounded-md transition-colors"
              >
                <Icon name="chevron-left" size="sm" />
              </button>
            )}
            {showNavigation === 'none' && <div className="w-6" />}

            <h3 className="text-sm font-medium">
              {format(month, 'MMMM yyyy', { locale: vi })}
            </h3>

            {showNavigation === 'right' && (
              <button
                onClick={goToNextMonth}
                className="p-1 hover:bg-accent rounded-md transition-colors"
              >
                <Icon name="chevron-right" size="sm" />
              </button>
            )}
            {showNavigation === 'none' && <div className="w-6" />}
          </div>

          {/* Week days header */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {weekDays.map((day) => (
              <div key={day} className="text-xs text-muted-foreground text-center p-2 font-medium">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar days */}
          <div className="grid grid-cols-7 gap-1">
            {days.map((day, index) => {
              if (!day) {
                return <div key={index} className="p-2" />;
              }

              const isCurrentMonth = isSameMonth(day, month);
              const isTodayDate = isToday(day);
              const isSelected = isStartDate(day) || isEndDate(day);
              const isInRangeDate = isInRange(day);

              return (
                <button
                  key={day.toISOString()}
                  onClick={() => handleDateClick(day)}
                  disabled={!isCurrentMonth}
                  className={`
                    p-2 text-sm rounded-md transition-colors relative
                    ${!isCurrentMonth
                      ? 'text-muted-foreground/50 cursor-not-allowed'
                      : 'hover:bg-accent'
                    }
                    ${isTodayDate && !isSelected ? 'bg-accent text-accent-foreground' : ''}
                    ${isSelected ? 'bg-primary text-primary-foreground' : ''}
                    ${isInRangeDate && !isSelected ? 'bg-primary/20' : ''}
                  `.trim()}
                >
                  {day.getDate()}
                </button>
              );
            })}
          </div>
        </div>
      );
    };

    return (
      <div ref={ref} className="relative">
        {/* Trigger Button */}
        <button
          ref={refs.setReference}
          className={buttonClasses}
          disabled={disabled}
          {...getReferenceProps()}
        >
          {triggerIcon || defaultIcon}
        </button>

        {/* Dropdown Content */}
        {isOpen && (
          <FloatingPortal>
            <FloatingFocusManager context={context} modal={false}>
              <div
                ref={refs.setFloating}
                style={floatingStyles}
                className="z-[9200] bg-popover border border-border rounded-lg shadow-lg overflow-hidden"
                {...getFloatingProps()}
              >
                <div className="flex">
                  {/* Left Calendar */}
                  <div className="border-r border-border">
                    {renderCalendar(currentMonth, 'left')}
                  </div>

                  {/* Right Calendar */}
                  <div>
                    {renderCalendar(nextMonth, 'right')}
                  </div>
                </div>

                {/* Selected Range Display */}
                {(startDate || endDate) && (
                  <div className="border-t border-border p-4 bg-muted/50">
                    <div className="text-sm text-muted-foreground">
                      {startDate && endDate ? (
                        <>
                          <span className="font-medium">Đã chọn:</span>{' '}
                          {format(startDate, 'dd/MM/yyyy')} - {format(endDate, 'dd/MM/yyyy')}
                        </>
                      ) : startDate ? (
                        <>
                          <span className="font-medium">Từ ngày:</span>{' '}
                          {format(startDate, 'dd/MM/yyyy')} (chọn ngày kết thúc)
                        </>
                      ) : null}
                    </div>
                  </div>
                )}
              </div>
            </FloatingFocusManager>
          </FloatingPortal>
        )}
      </div>
    );
  }
);

DoubleDatePicker.displayName = 'DoubleDatePicker';

export default DoubleDatePicker;
